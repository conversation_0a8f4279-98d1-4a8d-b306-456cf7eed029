give me a prd like document to be given to ai agent, an AI code assistant that will be used for building a browser extension that

creates this project with the following requirements:

Is building a browser extension with the following setup:
- Frontend: Browser Extension (Manifest V3) using HTML, CSS, and JS.
- ML: Uses Gemini 2.0 Flash Lite.
- Project Structure:
  - extension/ folder for browser extension code.
  - backend/ folder for backend code.
  - Storage: MongoDB for storage.
  - Don't leave anything for future, this is a prd for final product not a mvp.


assure that the agent will follow the above instructions and provide a complete and production-ready solution. The agent should also ensure that the code is well-documented, follows best practices, and is easy to maintain. The agent should also ensure that the app is fully functional and tested before delivering it.

ask the agent to ensure that the frontend is error-free


Is building a browser extension with the following setup:
- Frontend: Browser Extension (Manifest V3) using HTML, CSS, and JS.
- ML: Uses Gemini 2.0 Flash Lite.
- Backend: Express.js + Gemini 2.0 Flash Lite API for ai.
- Project Structure:
  - extension/ folder for browser extension code.
  - backend/ folder for backend code.


Is building a browser extension with the following setup:
- Frontend: Browser Extension (Manifest V3) using HTML, CSS, and JS.
- ML: Uses Gemini 2.0 Flash Thinking Experimental 01-21.
- Backend: Express.js + Gemini 2.0 Flash Thinking Experimental 01-21 API for ai.
- Project Structure:
  - extension/ folder for browser extension code.
  - backend/ folder for backend code.
  - Storage: MongoDB for storage.

  
The agent should ensure that the browser extension is built with the following setup:

-   Frontend: Browser Extension
-   Project Structure:
    -   extension/ folder for browser extension code.
    -   backend/ folder for the backend code for the AI model integration.

    -   ensure the backend is designed to handle AI model requests efficiently.
    -   ensure the backend is designed to handle AI model requests efficiently.
    -   ensure the extension is built with a focus on user experience and performance.
    -   ensure the backend is designed to handle AI model requests efficiently.
    -   Don't leave anything for future, this will be a prd for final product not a mvp.
