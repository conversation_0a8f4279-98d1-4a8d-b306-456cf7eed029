
give me a prd like document to be given to ai agent, an AI code assistant that will be used for building a browser extension that

creates this project with the following requirements:
Improve your writing with all-in-one assistance—including generative AI, grammar check, and more.

textwarden for Chrome offers real-time suggestions—including generative AI, grammar check, and more—to help you improve your writing online, no matter what you’re working on.
With comprehensive feedback on spelling, grammar, punctuation, clarity, and writing style, textwarden is more than just a proofreader. It’s a tool that helps you write with confidence, find the best words to express yourself, and communicate your ideas with ease. textwarden’s generative AI capabilities allow you to produce instant drafts, ideas, replies, and more wherever you do your best, most important writing.

➤ How it works
textwarden analyzes your sentences as you write and adds color-coded underlines to words and phrases where you can improve your writing. You can apply textwarden’s suggestion with a single click, or expand the suggestion to learn more about it.

➤ Go beyond grammar
textwarden’s advanced spelling checker and grammar checker go far beyond the built-in tools of word processors. textwarden can detect not only misspellings but also commonly confused words used in the wrong context, like “affect” and “effect.” In addition, it can flag and fix complex grammar and punctuation issues like sentence fragments, comma splices, and subject-verb disagreement.

But great writing is about much more than just grammar and spelling. That’s why textwarden also helps you streamline wordy phrases and rewrite sentences that are likely to confuse readers. It also suggests word choice improvements to help keep readers engaged, and helps you adjust your tone to ensure you come across the way you intend.

For students: Available on the extension, our free auto-citations feature generates citations for online sources in seconds, without you having to enter any info manually or leave the web page. Get pre-formatted citations ready to go, whether you use APA, MLA, or Chicago.

➤ What’s included?

• Grammar checker
• Spelling checker
• Punctuation checker
• Tone detector
• Auto-citations
• Generative AI features
textwarden includes an expanded range of writing feedback designed to help you produce high-quality writing that makes an impact.
• Clarity-focused sentence rewrites
• Tone adjustments
• Vocabulary improvements (word choice, formality level)
• Fluency suggestions
Features built for teams, including customized style guides and tone profiles to keep communication on-brand
• Additional advanced suggestions

➤ Works where you write
textwarden is designed to work seamlessly in your browser—no copying or pasting required. Use it in Google Docs, email clients, social media, and across the web. textwarden for Chrome works across:
• Google Docs
• Gmail
• LinkedIn
• And more!




Is building a browser extension with the following setup:
- Frontend: Browser Extension (Manifest V3) using HTML, CSS, and JS.
- ML: Uses gemini-2.5-flash-preview-04-17.
- Backend: Express.js + gemini-2.5-flash-preview-04-17 // npm install @google/genai mime
 for ai.
- Project Structure:
  - extension/ folder for browser extension code.
  - backend/ folder for backend code.
  - Storage: MongoDB for storage.
  - Don't leave anything for future, this is a prd for final product not a mvp.


assure that the agent will follow the above instructions and provide a complete and production-ready solution. The agent should also ensure that the code is well-documented, follows best practices, and is easy to maintain. The agent should also ensure that the app is fully functional and tested before delivering it.

ask the agent to ensure that the frontend is error-free
