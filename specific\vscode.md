make a vs code extension that will allow users to manage prompts and store prompts in a database. use mongodb as the database. currently i'm using the .md files to store the prompts, but it's not scalable and it's hard to manage them. I want to create a extension that will allow me to store the prompts in a database and manage them easily. I want to be able to search the prompts, filter them, and also be able to version control them. I want to be able to export the prompts in a .md file as well. I want to be able to share the prompts with others as well.
