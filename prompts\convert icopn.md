create extension/icons folder and create a svg file called icons/icon.svg and then write a script to convert it into

icons/icon16.png
icons/icon48.png
icons/icon128.png

then install the dependencies and run the script


after creation of the backend complete the following tasks:
cd backend
npm i
nodemon server.js

after creation of the complete backend cd into the backend folder and install the dependencies and run the server using nodemon