The AI coding agent should follow these guidelines when executing tasks:

- Do **not** wait for user confirmation before proceeding with planning or implementation.
- Begin execution immediately and proceed from **A to Z**, completing all aspects of the project without leaving any parts for future development.
- Follow **industry-standard coding best practices** including clean code, modularity, proper error handling, reusable components, security, and scalability.
- Use the **latest stable versions** of all programming languages, frameworks, and libraries.
- Structure code and files according to modern conventions with proper naming, separation of concerns, and environment configuration.
- Use GitHub username `chirag127` when initializing, configuring repositories, or pushing code.
- My name is <PERSON><PERSON>.
- Perform **web search autonomously** whenever needed to resolve implementation details, debug issues, or understand library updates/documentation.
- If a tool input exceeds limitations, **split the input** into smaller parts, **invoke the tool multiple times**, and **aggregate the results** into a coherent, logical, and well-structured output.
- **Rephrase or restructure** combined tool outputs for clarity, logic, and consistency when needed.
- Use **sequential thinking MCP server** extensively for step-by-step planning, workflow breakdowns, dependency resolution, and optimal execution ordering.
- Do **not** defer tasks or include future "TODO" notes—every deliverable must be fully implemented and production-ready.
- Provide **comprehensive documentation** including README files, and API documentation where applicable.
- Ensure all documentation is clear, concise, and easy to follow.
- Use **Hyperbrowser** for all web-related tasks like web scraping, data extraction, and API interactions while respecting robots.txt.
- Use **firecrawler** for all web crawling tasks and API interactions, ensuring best practices and robots.txt compliance.
- Perform **self-code reviews** to ensure code is clean, efficient, and adheres to best practices before finalizing.
- Always test the complete project at the end to ensure it functions without errors.
- Avoid placeholder code unless immediately expanded into full implementations.
- Execute projects fully rather than providing partial or incomplete solutions.
- Avoid code duplication by building upon existing implementations.
- Ensure code is as **modular** as possible for reusability and maintainability.
- Always give a modern frontend design.
create extension/icons folder and create a svg file called icons/icon.svg and then write a script to convert it into

icons/icon16.png
icons/icon48.png
icons/icon128.png

then install the dependencies and run the script


after creation of the backend complete the following tasks:
cd backend
npm i
nodemon server.js
example code for gemeni 2.0 flash lite
const {
  GoogleGenerativeAI,
  HarmCategory,
  HarmBlockThreshold,
} = require("@google/generative-ai");
const fs = require("node:fs");
const mime = require("mime-types");

const apiKey = process.env.GEMINI_API_KEY;
const genAI = new GoogleGenerativeAI(apiKey);

const model = genAI.getGenerativeModel({
  model: "gemini-2.0-flash-lite",
});

const generationConfig = {
  temperature: 1,
  topP: 0.95,
  topK: 40,
  maxOutputTokens: 8192,
  responseModalities: [
  ],
  responseMimeType: "text/plain",
};

async function run() {
  const chatSession = model.startChat({
    generationConfig,
    history: [
    ],
  });

  const result = await chatSession.sendMessage("INSERT_INPUT_HERE");
  // TODO: Following code needs to be updated for client-side apps.
  const candidates = result.response.candidates;
  for(let candidate_index = 0; candidate_index < candidates.length; candidate_index++) {
    for(let part_index = 0; part_index < candidates[candidate_index].content.parts.length; part_index++) {
      const part = candidates[candidate_index].content.parts[part_index];
      if(part.inlineData) {
        try {
          const filename = `output_${candidate_index}_${part_index}.${mime.extension(part.inlineData.mimeType)}`;
          fs.writeFileSync(filename, Buffer.from(part.inlineData.data, 'base64'));
          console.log(`Output written to: ${filename}`);
        } catch (err) {
          console.error(err);
        }
      }
    }
  }
  console.log(result.response.text());
}

run();

### ** Prompt for Browser Extension Landing Page & Privacy Policy**

#### **Objective**
Create a single `index.html` file for a browser extension's landing page, hosted on GitHub Pages. The landing page should:
- Provide an overview of the extension, its features, and installation instructions.
- Include a **Privacy Policy** link that opens in a new page (`privacy-policy.html`).
- Have a clean, modern design with a dark theme and a structured layout.

A separate `privacy-policy.html` should detail data usage, required permissions, and security measures.

---

### **Landing Page (`index.html`) Requirements**

1. **Title & Meta Tags**
   - Include appropriate metadata (`charset`, `viewport`).
   - Set `<title>` to the extension name.

2. **Styling & Theme**
   - Use a **dark mode theme** with CSS variables for easy customization.
   - Implement a **responsive layout** with grid-based feature sections.
   - Include hover effects and animations for UI elements.

3. **Content Sections**
   - **Header:**
     - Display the extension **logo**, **name**, and a short **tagline**.
     - Provide **buttons** for:
       - **GitHub repository** (or browser Web Store link).
       - **Privacy Policy** (opens `privacy-policy.html` in a new tab).

   - **About Section:**
     - Describe the extension's purpose and how it benefits users.

   - **Features Section:**
     - Use a **grid layout** to list key features with brief descriptions.

   - **Installation Instructions:**
     - Provide a step-by-step guide to installing the extension manually.

   - **How to Use:**
     - Explain how users can interact with the extension.

   - **Technology Stack:**
     - List the core technologies used (e.g., JavaScript, Chrome Extension APIs, AI services).

   - **Footer:**
     - Display copyright info, **Privacy Policy** link, and GitHub repository link.

---

## Create a `privacy-policy.html` file in the project root.

### **Privacy Policy (`privacy-policy.html`) Requirements**

1. **Title & Heading**
   - Use `Privacy Policy - [Extension Name]` as the `<title>`.
   - Include a back-to-home button (`index.html`).

2. **Content Structure**
   - **Introduction**
     - State the purpose of the Privacy Policy.
     - Mention the last updated date.

   - **Required Permissions**
     - Clearly list and explain **why** each permission is necessary.
     - Example permissions:
       - `storage` (save user preferences).
       - `activeTab` (interact with the active webpage).
       - `tabs` (detect active pages).
       - `host permissions` (access specific domains).

   - **Information We Collect**
     - Specify the data the extension collects (e.g., user preferences, page interactions).
     - Clarify **what is NOT collected** (e.g., personal data, browsing history).

   - **How We Use Your Information**
     - Explain how collected data improves the extension’s functionality.
     - Confirm that no data is sold/shared with third parties.

   - **Data Storage & Security**
     - Mention that data is stored **locally** or temporarily processed on a backend.
     - Highlight encryption/security measures.

   - **Third-Party Services**
     - Disclose any external APIs or services used (e.g., AI models, analytics).
     - Provide links to third-party privacy policies.

   - **User Rights**
     - State users' rights regarding data access, modification, and deletion.

   - **Changes to This Privacy Policy**
     - Explain how users will be notified of policy updates.

   - **Contact Information**
     - Provide a method for users to reach out (GitHub issues etc.).

---

### **Additional Notes**
- Ensure **mobile responsiveness** for both pages.
- Use **semantic HTML** for accessibility and SEO.
- Keep text **clear and concise**, avoiding unnecessary legal jargon.

---

 Create a README.md file in the project root.

 Must contain the following sections:

📘 Project Title

✨ Description

🚀 Live Demo (Link to GitHub Pages website)

🛠️ Tech Stack / Tools Used

📦 Installation Instructions

🔧 Usage

🧪 Features

📸 Screenshots (optional but encouraged)

🙌 Contributing

🪪 License

 Include a live preview link to the deployed GitHub Pages website (e.g., https://your-username.github.io/your-repo-name).


Use the web search if any help is needed in the implementation of this browser extension. Also use the sequential thinking mcp server wherever possible 
