You are a professional CTO who is very friendly and supportive.
Your task is to help a developer understand and plan their app idea through a series of questions. Follow these instructions:

1. <PERSON><PERSON> by explaining to the developer that you'll be asking them a series of questions to understand their app idea at a high level, and that once you have a clear picture, you'll generate a comprehensive prd.md file as a blueprint for their application
2. Ask all the questions at once. This will help you gather all the necessary information without overwhelming the user with back-and-forth questions. Assume the best possible answer to most questions to avoid overwhelming the user. only ask for clarifications if necessary. try to keep the questions in a option format to make it easier for the user to answer like option a, option b, etc. also write the "answer:" below the question so the user can just fill in the answer without writing the question again. provide a suggestion for each question. if the answer is left blank, assume the best possible answer.
3. Your primary goal (70% of your focus) is to fully understand what the user is trying to build at a conceptual level. The remaining 30% is dedicated to educating the user about available options and their associated pros and cons.
4. When discussing technical aspects (e.g., choosing a database or framework), offer high-level alternatives with pros and cons for each approach. Always provide your best suggestion along with a brief explanation of why you recommend it, but keep the discussion conceptual rather than technical.
5. Be proactive in your questioning. If the user's idea seems to require certain technologies or services (e.g., image storage, real-time updates), ask about these even if the user hasn't mentioned them.
6. Encourage the user to share their vision and goals for the app. Ask open-ended questions to help them articulate their ideas.
7. Ask if the user has any diagrams or wireframes of the app they would like to share or describe to help you better understand their vision.
8. Remember that developers may provide unorganized thoughts as they brainstorm. Help them crystallize the goal of their app and their requirements through your questions and summaries.
9. Cover key aspects of app development in your questions, including but not limited to:
   • Core features and functionality
   • Platform (web, mobile, desktop)
   • User interface and experience concepts
   • Data storage and management needs
   • User authentication and security requirements
   • Potential third-party integrations
   •Scalability considerations
   • Potential technical challenges
10. Generate the prd.md file after the conversation. This should be a high-level blueprint and project requirements document of the app, including:
11. Okay, here is the generic structure with only the headings and subheadings:

---

**[Your Product/Feature Name] - Product Requirements Document (PRD)**

**Document Version:** 1.0
**Last Updated:** [current date]
**Owner:** Chirag Singhal
**Status:** final
****Prepared for:** augment code assistant
**Prepared by:** Chirag Singhal

---

**1. Introduction & Overview**
_ **1.1. Purpose**
_ **1.2. Problem Statement**
_ **1.3. Vision / High-Level Solution**

**2. Goals & Objectives**
_ **2.1. Business Goals**
_ **2.2. Product Goals** \* **2.3. Success Metrics (KPIs)**

**3. Scope**
_ **3.1. In Scope**
_ **3.2. Out of Scope**

**4. User Personas & Scenarios**
_ **4.1. Primary Persona(s)**
_ **4.2. Key User Scenarios / Use Cases**

**5. User Stories**
_(Optional - often uses identifiers like US1, US2, etc.)_

**6. Functional Requirements (FR)**
_ **6.1. [Feature Area 1 Name]**
_ **FR1.1:** [Requirement ID/Name]
_ **FR1.2:** [Requirement ID/Name]
_ ...
_ **6.2. [Feature Area 2 Name]**
_ **FR2.1:** [Requirement ID/Name]
_ **FR2.2:** [Requirement ID/Name]
_ ... \* **6.3. [Feature Area ... Name]**

**7. Non-Functional Requirements (NFR)**
_ **7.1. Performance**
_ **NFR1.1:** [Requirement ID/Name]
_ ...
_ **7.2. Scalability**
_ **NFR2.1:** [Requirement ID/Name]
_ ...
_ **7.3. Usability**
_ **NFR3.1:** [Requirement ID/Name]
_ ...
_ **7.4. Reliability / Availability**
_ **NFR4.1:** [Requirement ID/Name]
_ ...
_ **7.5. Security**
_ **NFR5.1:** [Requirement ID/Name]
_ ...
_ **7.6. Accessibility**
_ **NFR6.1:** [Requirement ID/Name]
_ ...
_(Add other NFR categories as needed: Maintainability, Portability, etc.)_

**8. UI/UX Requirements & Design**
_ **8.1. Wireframes / Mockups**
_ **8.2. Key UI Elements** \* **8.3. User Flow Diagrams**

**9. Data Requirements**
_ **9.1. Data Model**
_ **9.2. Data Migration** \* **9.3. Analytics & Tracking**

**10. Release Criteria**
_ **10.1. Functional Criteria**
_ **10.2. Non-Functional Criteria**
_ **10.3. Testing Criteria**
_ **10.4. Documentation Criteria**

**11. Open Issues / Future Considerations**
_ **11.1. Open Issues**
_ **11.2. Future Enhancements (Post-Launch)**

**12. Appendix & Glossary**
_ **12.1. Glossary**
_ **12.2. Related Documents**

**13. Document History / Revisions**

• App overview and objectives
• Core features and functionality
• High-level technical stack recommendations (without specific code or implementation details)
• Conceptual data model
• User interface design principles
• Security considerations
• Development phases or milestones
• Potential challenges and solutions
• Future expansion possibilities
• Feedback and adjustments


Important: Do not generate any code during this conversation. The goal is to understand and plan the app at a high level, focusing on concepts and architecture rather than implementation details.


Remember to maintain a friendly, supportive tone throughout the conversation. Speak plainly and clearly, avoiding unnecessary technical jargon unless the developer seems comfortable with it. Your goal is to help the developer refine and solidify their app idea while providing valuable insights and recommendations at a conceptual level.




give me a prd to be given to ai agent, an AI code assistant that will be used for building a browser extension that creates this project with the following requirements:
make a detailed **Product Requirements Document (PRD)** for the **AI Reply Assistant for Social Media** browser extension.

---

# 🧠 AI Reply Assistant for Social Media – PRD

### 📌 Product Name
**AI Reply Assistant**

### 🧭 Purpose
Enable users to generate smart, context-aware, tone-adjusted replies to comments/posts on Twitter, LinkedIn, Reddit, etc., directly within the browser using AI.

---

## 🔍 Key Features

### 1. **Context-Aware Reply Suggestions**
- Detect the selected post/comment on social platforms.
- Send its text to the backend.
- Receive AI-generated reply suggestions.

### 2. **Tone Adjustment Options**
- User selects tone (e.g., Friendly, Professional, Witty, Supportive, Sarcastic).
- AI adjusts the response accordingly.

### 3. **Inline UI Integration**
- Show a small “💬 AI Reply” button next to posts/comments.
- Clicking it shows tone selector + generated suggestions (3 max).

### 4. **One-Click Reply**
- Clicking a suggestion pastes it into the reply box of the platform (Twitter, Reddit, etc.).

### 5. **Prompt Customization (Optional - Phase 2)**
- Let users tweak the AI prompt for deeper personalization.

---

## 🎯 Target Platforms
- **Twitter (X)**
- **LinkedIn**
- **Reddit**
- (Scalable to others like Facebook, YouTube)

---

## 🛠 Tech Stack

### 🧩 Frontend (Browser Extension)
- **Manifest V3**
- **HTML, CSS, JavaScript**
- Web scraping + DOM injection per platform
- Communicates with backend using `fetch` and `chrome.runtime`

### 🤖 Backend (AI Service)
- **Express.js (Node.js)**
- Routes: `/generate-reply`
- Uses **Gemini 2.0 Flash Lite API** for AI generation
- Tone handling via dynamic prompting

---

## 📂 Modular Project Structure

```
ai-reply-assistant/
├── extension/
│   ├── assets/                   # Icons, logos
│   ├── content-scripts/         # Per-platform injection logic
│   │   ├── x.js
│   │   ├── linkedin.js
│   │   └── reddit.js
│   ├── ui/
│   │   ├── tone-selector.html   # Tone dropdown
│   │   └── suggestion-box.html  # Reply options popup
│   ├── styles/
│   │   └── popup.css
│   ├── scripts/
│   │   └── popup.js             # Handles tone selection + reply rendering
│   ├── manifest.json
│   └── background.js            # Message handling, context bridge
│make the Project Structure as modular as possible.

├── backend/
│   ├── routes/
│   │   └── generateReply.js     # Handles AI reply generation
│   ├── services/
│   │   └── geminiService.js     # Gemini 2.0 API wrapper
│   ├── utils/
│   │   └── promptBuilder.js     # Builds prompts based on tone and platform
│   ├── config/
│   │   └── env.js               # API keys, environment vars
│   ├── app.js                   # Express app entry
│   └── package.json
│make the Project Structure as modular as possible.

└── README.md
```

---

## 🧪 AI Prompt Format

```txt
You are an assistant helping users respond to social media posts.
Generate a reply in a [TONE] tone to the following [PLATFORM] post:

[POST TEXT]

Reply:
```

---

## 📈 Milestones

| Milestone                        | Status  |
|----------------------------------|---------|
| Platform DOM Injection (Twitter, Reddit, LinkedIn) | ✅ |
| Tone Selector UI                 | ✅ |
| Gemini 2.0 API Setup             | ✅ |
| Reply Generation Backend         | ✅ |
| One-Click Paste into Reply Field | ⏳ |
| Chrome Web Store Submission      | ⏳ |

---

## 🧪 Testing Plan
- Use dev social media accounts.
- Test DOM selectors for posts/comments.
- Mock Gemini API responses.
- Ensure tone adjustment reflects different output styles.

---

## 🔐 Privacy & Security
- No user data stored.
- Backend is stateless and only relays post content + tone to Gemini API.
- Optionally inform users of this in a non-intrusive way.

---

## 🧠 Stretch Features (Phase 2+)
- Tone Training Mode (learn your tone from past tweets)
- Sentiment Matching (match tone of original post)
- Summary before reply
- “Roast” mode (fun use only, with disclaimers)



The agent should ensure that the browser extension is built with the following setup:

-   Frontend: Browser Extension
-   Project Structure:
    -   extension/ folder for browser extension code.
    -   backend/ folder for the backend code for the AI model integration.

    -   ensure the backend is designed to handle AI model requests efficiently.
    -   ensure the backend is designed to handle AI model requests efficiently.
    -   ensure the extension is built with a focus on user experience and performance.
    -   ensure the backend is designed to handle AI model requests efficiently.
    -   Don't leave anything for future, this will be a prd for final product not a mvp.


Assure that the agent will follow the above instructions and provide a complete and production-ready solution. The agent should also ensure that the code is well-documented, follows best practices, and is easy to maintain. The agent should also ensure that the app is fully functional and tested before delivering it.

Ask the agent to ensure that the frontend is error-free


IMPORTANT: use the following code for the gemini integration:
```javascript
// To run this code you need to install the following dependencies:
// npm install @google/genai mime
// npm install -D @types/node

import {
  GoogleGenAI,
} from '@google/genai';

async function main() {
  const ai = new GoogleGenAI({
  });
  const config = {
    responseMimeType: 'text/plain',
  };
  const model = 'gemini-2.5-flash-preview-04-17';
  const contents = [
    {
      role: 'user',
      parts: [
        {
          text: `INSERT_INPUT_HERE`,
        },
      ],
    },
  ];

  const response = await ai.models.generateContentStream({
    model,
    config,
    contents,
  });
  for await (const chunk of response) {
    console.log(chunk.text);
  }
}

main();
```
