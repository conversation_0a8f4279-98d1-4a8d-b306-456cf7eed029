give me a prd for

read aloud copied text

 Is building a browser extension with the following setup:
- Frontend: Browser Extension (Manifest V3) using HTML, CSS, and JS.
- Project Structure:
  - extension/ folder for browser extension code.
  - backend/ folder for backend code. if backend is needed
make Project Structure as modular as possible

Is building a browser extension with the following setup:
- Frontend: Browser Extension (Manifest V3) using HTML, CSS, and JS.
- ML: Uses Gemini 2.0 Flash Thinking Experimental 01-21.
- Backend: Express.js + Gemini 2.0 Flash Thinking Experimental 01-21 API for ai.
- Project Structure:
  - extension/ folder for browser extension code.
  - backend/ folder for backend code.
  - Storage: MongoDB for storage.

don't leave anything for future , this is a prd for final product not a mvp



