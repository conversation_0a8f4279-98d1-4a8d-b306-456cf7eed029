use the following Tools and Packages to build a React Native app in 2025


### Core Setup

*   **Framework:** **Expo** is strongly recommended for all new React Native projects in 2025. It simplifies development, provides a smoother experience, and is favored by major companies.
*   **Navigation:**
    *   **Expo Router:** Preferred by the speaker. It leverages file-based routing, offering a more cross-platform approach and simplified setup. Includes features like API routes and dynamic components.
    *   **React Navigation:** A viable option, particularly if you prioritize screen-based navigation.
    *   **React Native Bottom Tabs:** Recommended by the speaker because it gives you the ability to have real native bottom tabs in your application which looks a lot smoother on both Android and iOS.

### UI and Styling

*   **Styling Solutions:** The video steers away from large component libraries (like React Native Paper or React Native Elements), and recommends focusing on styling solutions such as:
    *   **NativeWind:** Brings Tailwind CSS to React Native.
*   **Recommendations:** NativeWind are popular solutions. The choice is often a matter of personal preference and project needs.

### Animations

*   **React Native Reanimated:** The go-to library for animations.


### Essential Utilities

*   **Testing:**
    *   **Maestro:**  Highly recommended for UI testing; it's easy to set up and use.
    *   **React Native Testing Library:** Offers great documentation, code examples and is the recommended library for testing.
*   **Monitoring:**  **Sentry** is essential for monitoring bugs and application performance.

### Must-Have Packages

*   **React Native MMKV (by Marc Riviere and the Mello team):**  The fastest key-value storage for quickly tracking settings.
*   **React Hook Form:** Works seamlessly with React Native for form management, and can be integrated with Zod for type-safe forms.
*   **Expo Image:** An excellent choice for displaying images, designed for speed, built-in memory, and caching.
*   **Zego:**  Provides amazing drop-down menus and context menus, perfect for overlays. The documentation might be challenging, but the features are well worth it.
*   **Clerk:**  The preferred choice for user authentication and management, including social providers.
*   **React Native Gesture Handler:** Essential for capturing gestures, including pen gestures, rotations, flings, and long presses. Often included in Expo projects.
*   **Error Tracking:** **Bugsnag** is also a great tool for tracking errors in production.



give me a prd like document to be given to ai agent, an AI code assistant that will be used for building a react native app that

scans the qr code.


The agent should ensure that the browser extension is built with the following setup:

- Frontend: React Native Expo app using javascript.
- Project Structure:
  - frontend/ folder for React Native app code.
    -   Don't leave anything for future, this is a prd for final product not a mvp.

Assure that the agent will follow the above instructions and provide a complete and production-ready solution. The agent should also ensure that the code is well-documented, follows best practices, and is easy to maintain. The agent should also ensure that the app is fully functional and tested before delivering it.

Ask the agent to ensure that the frontend is error-free
