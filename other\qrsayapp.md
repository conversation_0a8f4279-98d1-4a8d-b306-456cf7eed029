This is a website is called qrsay which offer the ordering food platform. The code for the user frontend is code in the digitalMenu\src\app\restaurant and the admin frontend website code in the digitalMenu\src\app\admin, The back end is in qrsayBackend folder. there is another react native expo app for the admin frontend in the qrsay-admin-frontend-react-native-expo-app folder.
there will be a react native expo app for the user frontend in the qrsay-user-frontend-react-native-expo-app folder.

make a react native expo app with the following setup:
- Frontend: React Native Expo app using JavaScript for the user frontend, this app will copy all the functionality of the user frontend website. The code will be in the qrsay-user-frontend-react-native-expo-app folder.
- Backend: use the existing backend code in the qrsayBackend folder.
- important: implement the same functionality as the user frontend website, including all the features and functionality. don't leave anything for future work. The app should be fully functional and ready for production. use the exact same apis as the user frontend website. take context from the user frontend website code and the qrsayBackend folder and implement the same functionality in the react native expo app.
