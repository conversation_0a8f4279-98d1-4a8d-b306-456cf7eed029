# AI CTO Prompt Enhancement Request

Please conduct a comprehensive review and enhancement of the `AI_CTO.md` file in the prompts directory. Your task is to transform this prompt into a more effective, structured, and actionable guide for an AI acting in a CTO capacity.

## Analysis Requirements
1. Perform a thorough content analysis, identifying:
   - Core responsibilities and decision-making parameters
   - Technical domains covered
   - Existing instruction clarity and specificity
   - Current prompt structure and organization
   - Any contradictory or ambiguous directives

## Enhancement Objectives
Please implement the following improvements:

1. **Instruction Clarity**:
   - Replace vague directives with specific, actionable instructions
   - Define technical terms precisely where they first appear
   - Establish clear boundaries of authority and decision-making scope

2. **Structural Organization**:
   - Reorganize content into logical sections with clear headings
   - Implement a hierarchical structure that prioritizes critical information
   - Add a table of contents for navigability

3. **Technical Guidance Enhancement**:
   - Update technology references to reflect current industry standards (2025)
   - Expand guidance on emerging technologies (AI/ML, cloud architecture, etc.)
   - Include specific evaluation criteria for technology selection decisions

4. **Output Specification**:
   - Define expected response formats for different query types
   - Specify detail level requirements for technical recommendations
   - Include examples of ideal responses for common CTO scenarios

5. **Context Enrichment**:
   - Add industry-specific considerations for different business domains
   - Include guidance on balancing technical debt vs. innovation
   - Provide frameworks for technology roadmap development

6. **Error Correction**:
   - Fix any grammatical or syntax errors
   - Correct technical inaccuracies
   - Resolve logical inconsistencies or contradictions

7. **Best Practices Implementation**:
   - Incorporate prompt engineering best practices
   - Add system message optimization techniques
   - Include guardrails for responsible technology recommendations

## Deliverable Format
Please provide the enhanced prompt as a complete replacement of the existing file, maintaining Markdown formatting with appropriate use of headings, lists, code blocks, tables, and emphasis where needed. use websearch tool to find information on the internet. use clear thought mcp server.