give me a prd like document to be given to ai agent that will be used for building a react native expo app that
Please create a comprehensive Product Requirements Document (PRD) for a cross-platform mobile application that summarizes YouTube videos using Gemini 2.0 Flash-Lite AI. The PRD should be professionally formatted with clear section headers, subsections, and numbered requirements following standard PRD conventions. The document should be suitable for presentation to development teams.

user can share the youtube videos links and it provides the summary of the video, the summary is generated by Gemini 2.0 Flash-Lite ,the summary is stored in mongodb

-   The app allows the user to read aloud the summary using text-to-speech functionality.
-   the app should highlight the text being read aloud words by words. #important
-   the app should scroll the text being read aloud to the top of the screen.
-   don't read aloud the markdown symbols like #, *, etc.
-   the app should have a read-aloud button that starts the text-to-speech functionality from the beginning of the summary.

-   The app should provide a way for users to adjust the text-to-speech settings (speed, pitch, voice).
-   The app should provide a way for users to pause and resume the read-aloud feature.
-   The app should provide a way for users to stop the read-aloud feature.
-   The app should provide a way for users to adjust the volume of the read-aloud feature.
-   the read aloud feature should be available for all summary types.
-   The app should provide a way for users to adjust the speed of the text-to-speech feature up to 16x speed.
-   The app should provide a way for users to adjust the pitch of the text-to-speech feature.
-   The app should provide a way for users to adjust the voice of the text-to-speech feature.
-   Provide a way for users to select between summary types: Brief, Detailed, Key Point.
-   Default type should be set to Brief.
-   The app should provide a way for users to select between summary lengths: Short, Medium, Long.
-   Default length should be set to Medium.

-   The app should allow users to paste YouTube video links directly into a text input field for summary generation.
    Besides pasting, users should be able to share a YouTube link directly from the YouTube app into your app
    it is building a react native expo app with the following setup:
    strictly should the initial client-side URL validation be? Just check for youtube.com or youtu.be, or attempt a more complex regex? (Backend validation will be the final check).
    mongodb is used for storage of summaries with video links.(show the video link in the summary in hamburg menu)
    there Should be a way for the user to cancel a summary generation request that is in progress
    When displaying a summary, the video's title and thumbnail should always be fetched and displayed alongside it?
    In the history list, information should be shown for each entry Thumbnail + Title
    users Shouldbe able to delete summaries from their history?
    The transcript don't need to be in the English language. They can be in the any language The transcript can be in the any language, but the summay will always be in the English language. Update the prompt of the gemini model to always give the summary in the English language The transcript don't need to be in the English language only The subtitle don't need to be in the English language only. But always prefer the English language transcript
    the fallback plan if a video doesn't have transcripts or captions available is the Should app inform the user it cannot summarize this video?
    fetching the video_title, video_thumbnail is not critical? summary generation Should not fail if the title, thumbnail can't be retrieved?
-   The app should have a modern and user-friendly design, with a focus on usability and accessibility.
-   The app should support dark mode and light mode for better user experience.

-   this app will be android, ios and web compatible.
-   The app should be built using React Native and Expo for cross-platform compatibility.
    Sharing generated summaries directly from the app.
-   give the summary in a card format with the following details:
    -   Video Title
    -   Video Thumbnail
    -   Summary Text(parsed in markdown format)
    -   Summary Type (Brief, Detailed, Key Point)
    -   Summary Length (Short, Medium, Long)
    -   Read Aloud Button
    -   Share Button
    -   Delete Button
    -   Edit Button (to change summary type and length)
-   The app should have a history screen that displays all previously generated summaries in a list format.
-   the summary should be in the markdown format.
-   the app should have a settings screen where users can adjust the text-to-speech settings (speed, pitch, voice).
-   give the each section of the document a proper heading and subheading and make it look like a proper document likebelow:
    **4. Functional Requirements**

    **4.1. Video Link Input & Validation**

*   **FR1.1 Paste Link:** Users must be able to paste a YouTube video URL directly into a designated text input field on the main screen.
*   **FR1.2 Share-to-App:** Users must be able to share a YouTube video link directly from the native YouTube application (iOS/Android) or web browser into this app, triggering the summarization process.

The app is should be available in the list of the shareable link of the youtube, like when I share the link from the youtube app, the app should appear in the list of the apps available to share the link with. like Whatsapp Telegram. Other apps are available, but this app should be available in the list

The following is the example of gemini 2.0 flash-lite API call:
pip install google-genai
```import base64
import os
from google import genai
from google.genai import types


def generate():
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.0-flash-lite"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_text(text="""INSERT_INPUT_HERE"""),
            ],
        ),
    ]
    generate_content_config = types.GenerateContentConfig(
        response_mime_type="text/plain",
    )

    for chunk in client.models.generate_content_stream(
        model=model,
        contents=contents,
        config=generate_content_config,
    ):
        print(chunk.text, end="")

if __name__ == "__main__":
    generate()

```

ask the agent to Don't withhold yourself from searching anything on the web. If you think that the API or something I might have changed, please search it on the web. If you think anything might have been not in your data

-   Frontend: React Native Expo app using JavaScript.
-   ML: Uses Gemini 2.0 Flash-Lite.
-   Backend: fast api + Gemini 2.0 Flash-Lite API for ai + mongodb for storage + yt-dlp python library for youtube video transcript and thumbnail fetching and other details.
- the app should be built using the javascript programming language in the frontend and fast api python framework in the backend.

-   Project Structure:
    -   frontend/ folder for React Native app code.
    -   backend/ folder for backend code.
    -   Storage: mongodb for storage
-   Additional Features:
    -   Implement error handling for video link validation.
    -   Ensure the app is optimized for performance across all platforms.
        don't leave anything for future , this is a prd for final product not a mvp


assure that the agent will follow the above instructions and provide a complete and production-ready solution. The agent should also ensure that the code is well-documented, follows best practices, and is easy to maintain. The agent should also ensure that the app is fully functional and tested before delivering it.

ask the agent to ensure that the frontend is error-free
