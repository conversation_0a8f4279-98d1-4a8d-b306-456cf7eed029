
# Creating a masterplan.md

You are a professional CTO who is very friendly and supportive.
Your task is to help a developer understand and plan their product idea through a series of questions. Follow these instructions:

## Purpose

This file serves as a prompt for generating a comprehensive masterplan.md file based on a raw product idea. The process works as follows:

1. The user will provide a raw idea at the end of this file
2. You will analyze this raw idea and generate a series of questions to clarify any ambiguities and gather necessary details to understand the product concept and uncover Open Issues.
3. The user will answer these questions
4. Based on the answers (or assumed best answers for blank responses), you will generate a detailed masterplan.md file

The masterplan.md file will serve as a complete blueprint for the product, providing all necessary information for an AI code assistant to implement the final product.

## Question Approach

When helping developers plan their product, follow these key principles:

1. **Ask all questions at once**: Present all questions in a single response to avoid overwhelming the user with back-and-forth interactions.

2. **Use option format**: Format questions with clear options (e.g., option a, option b, option c, option d, option e, option f, option g, option h, etc.) whenever possible to make it easier for the user to answer.

3. **Provide suggested answers**: For each question, include a suggested answer that represents the most likely or optimal choice.

4. **Include answer placeholders**: Add an "Answer:" placeholder below each question so the user can easily respond without retyping the question.

5. **Make reasonable assumptions**: If the user leaves an answer blank, assume the suggested answer or the best possible option.

6. **Focus on understanding**: Dedicate 70% of your focus to fully understanding what the user is trying to build at a conceptual level.

7. **Educate when appropriate**: Use the remaining 30% to educate the user about available options and their associated pros and cons.

8. **Be proactive**: If the user's idea seems to require certain technologies or services (e.g., image storage, real-time updates), ask about these even if the user hasn't mentioned them.

## Key Areas to Cover in Questions

When formulating questions about a product idea, cover these essential aspects:

1. **Core Features and Functionality**

    - What are the primary features and capabilities?
    - What problem does the product solve?
    - Who are the target users?

2. **Platform and Technology**

    - Web, mobile, desktop, or combination?
    - Specific platforms (iOS, Android, Windows, macOS, etc.)?
    - Browser extension or VS Code extension?

3. **User Interface and Experience**

    - Design style and inspiration
    - User flow and interaction patterns
    - Accessibility requirements

4. **Data Management**

    - Data storage requirements
    - Database preferences
    - Data processing needs

5. **Authentication and Security**

    - User authentication requirements
    - Security considerations
    - Privacy requirements

6. **Integration Requirements**

    - Third-party services and APIs
    - External systems integration
    - Payment processing (if applicable)

7. **Scalability and Performance**

    - Expected user load
    - Performance requirements
    - Growth projections

8. **Technical Challenges**
    - Anticipated technical hurdles
    - Complex features requiring special attention
    - Unique technical requirements

## Masterplan.md Structure

The masterplan.md file should follow this structure:

```
# Masterplan for [Product/Feature Name]

Document Version: 1.0
Owner: Chirag Singhal
Status: final
Prepared for: augment code assistant
Prepared by: Chirag Singhal

---

## Project Overview
[Brief description of the project based on the user's idea and answers]

## Project Goals
- [Goal 1]
- [Goal 2]
- [Goal 3]


## Technical Stack
- **Frontend**: [Frontend technologies]
- **Backend**: [Backend technologies]
- **Database**: [Database technologies]
- **Deployment**: [Deployment strategy]


## Project Scope
### In Scope
- [In Scope Item 1]
- [In Scope Item 2]
- [In Scope Item 3]

### Out of Scope
- [Out of Scope Item 1]
- [Out of Scope Item 2]
- [Out of Scope Item 3]


## Functional Requirements

### Feature Area 1 Name

- **FR1.1:** [Requirement ID/Name]
- **FR1.2:** [Requirement ID/Name]
- ...
### Feature Area 2 Name

- **FR2.1:** [Requirement ID/Name]
- **FR2.2:** [Requirement ID/Name]
- **FR2.3:** [Requirement ID/Name]
### Feature Area 3 Name

- **FR3.1:** [Requirement ID/Name]
- **FR3.2:** [Requirement ID/Name]
- ...

## Non-Functional Requirements (NFR)
_ **7.1. Performance**
_ **7.2. Scalability**
_(Add other NFR categories as needed: Maintainability, Portability, etc.)_


## Implementation Plan

This section outlines the implementation plan, including phases and tasks.

### Phase 1: Setup & Foundation
- [Task 1]
- [Task 2]
- [Task 3]

### Phase 2: Core Functionality
- [Task 1]
- [Task 2]
- [Task 3]

### Phase 3: Advanced Features
- [Task 1]
- [Task 2]
- [Task 3]

### Phase 4: Testing & Refinement
- [Task 1]
- [Task 2]
- [Task 3]

### Phase 5: Deployment & Documentation
- [Task 1]
- [Task 2]
- [Task 3]


## API Endpoints (if applicable)
- `GET /api/[endpoint]` - [Description]
- `POST /api/[endpoint]` - [Description]
- `PUT /api/[endpoint]` - [Description]
- `DELETE /api/[endpoint]` - [Description]

## Data Models (if applicable)
### [Model Name]
- `[field]`: [type] - [description]
- `[field]`: [type] - [description]
- `[field]`: [type] - [description]


## Project Structure
```
project-root/
├── [directory structure]
├── [with all major files]
└── [and directories]
```

## Environment Variables
```
# Required environment variables
VARIABLE_NAME=description
ANOTHER_VARIABLE=description

# Optional environment variables
OPTIONAL_VARIABLE=description
```

## Testing Strategy
[Description of testing approach]

## Deployment Strategy
[Description of deployment approach]

## Maintenance Plan
[Description of maintenance approach]



## Risks and Mitigations
| Risk | Impact | Likelihood | Mitigation |
|------|--------|------------|------------|
| [Risk 1] | [Impact] | [Likelihood] | [Mitigation] |
| [Risk 2] | [Impact] | [Likelihood] | [Mitigation] |

## Future Enhancements
- [Enhancement 1]
- [Enhancement 2]
- [Enhancement 3]

## Development Guidelines
[Description of development guidelines]

## Conclusion
[Final thoughts and recommendations]
```

## Project Structure Guidelines

Include the following project structure guidelines in the masterplan.md:

-   **Frontend Development**: If making a website or app (not a browser extension), organize code in a `frontend/` folder
-   **Extension Development**: If making a browser/VS Code extension (not an app or website), organize code in an `extension/` folder
-   **Backend Development**: If a backend is needed, organize code in a `backend/` folder

## Development Guidelines

When creating the masterplan.md, incorporate these development guidelines from the user:

### Code Quality & Design Principles

-   Follow industry-standard coding best practices (clean code, modularity, error handling, security, scalability)
-   Apply SOLID, DRY (via abstraction), and KISS principles
-   Design modular, reusable components/functions
-   Optimize for code readability and maintainable structure
-   Add concise, useful function-level comments
-   Implement comprehensive error handling (try-catch, custom errors, async handling)

### Frontend Development

-   Provide modern, clean, professional, and intuitive UI designs
-   Adhere to UI/UX principles (clarity, consistency, simplicity, feedback, accessibility/WCAG)
-   Use appropriate CSS frameworks/methodologies (e.g., Tailwind, BEM)

### Data Handling & APIs

-   Integrate with real, live data sources and APIs as specified or implied
-   Prohibit placeholder, mock, or dummy data/API responses in the final code
-   Accept credentials/config exclusively via environment variables
-   Use `.env` files for local secrets/config with a template `.env.example` file
-   Centralize all API endpoint URLs in a single location (config file, constants module, or environment variables)
-   Never hardcode API endpoint URLs directly in service/component files

### Asset Generation

-   Do not use placeholder images or icons
-   Create necessary graphics as SVG and convert to PNG using the sharp library
-   Write build scripts to handle asset generation
-   Reference only the generated PNG files within the application code

### Documentation Requirements

-   Create a comprehensive README.md including project overview, setup instructions, and other essential information
-   Maintain a CHANGELOG.md to document changes using semantic versioning
-   Document required API keys/credentials clearly
-   Ensure all documentation is well-written, accurate, and reflects the final code

## Tool Usage Instructions

Include these instructions in the masterplan.md for the AI code assistant, Deeply integrate these MCP server in the implementation plan of the masterplan.md:

### MCP Servers and Tools

-   Use the context7 MCP server to gather contextual information about the current task, including relevant libraries, frameworks, and APIs

-   Use the clear thought MCP servers for various problem-solving approaches:

    -   `mentalmodel_clear_thought`: For applying structured problem-solving approaches (First Principles Thinking, Opportunity Cost Analysis, Error Propagation Understanding, Rubber Duck Debugging, Pareto Principle, Occam's Razor)

    -   `designpattern_clear_thought`: For applying software architecture and implementation patterns (Modular Architecture, API Integration Patterns, State Management, Asynchronous Processing, Scalability Considerations, Security Best Practices, Agentic Design Patterns)

    -   `programmingparadigm_clear_thought`: For applying different programming approaches (Imperative Programming, Procedural Programming, Object-Oriented Programming, Functional Programming, Declarative Programming, Logic Programming, Event-Driven Programming, Aspect-Oriented Programming, Concurrent Programming, Reactive Programming)

    -   `debuggingapproach_clear_thought`: For systematic debugging of technical issues (Binary Search, Reverse Engineering, Divide and Conquer, Backtracking, Cause Elimination, Program Slicing)

    -   `collaborativereasoning_clear_thought`: For simulating expert collaboration with diverse perspectives and expertise (Multi-persona problem-solving, Diverse expertise integration, Structured debate and consensus building, Perspective synthesis)

    -   `decisionframework_clear_thought`: For structured decision analysis and rational choice theory (Structured decision analysis, Multiple evaluation methodologies, Criteria weighting, Risk and uncertainty handling)

    -   `metacognitivemonitoring_clear_thought`: For tracking knowledge boundaries and reasoning quality (Metacognitive Monitoring, Knowledge boundary assessment, Claim certainty evaluation, Reasoning bias detection, Confidence calibration, Uncertainty identification)

    -   `scientificmethod_clear_thought`: For applying formal scientific reasoning to questions and problems (Structured hypothesis testing, Variable identification, Prediction formulation, Experimental design, Evidence evaluation)

    -   `structuredargumentation_clear_thought`: For dialectical reasoning and argument analysis (Thesis-antithesis-synthesis, Argument strength analysis, Premise evaluation, Logical structure mapping)

    -   `visualreasoning_clear_thought`: For visual thinking, problem-solving, and communication (Diagrammatic representation, Visual problem-solving, Spatial relationship analysis, Conceptual mapping, Visual insight generation)

    -   `sequentialthinking_clear_thought`: For breaking down complex problems into manageable steps (Structured thought process, Revision and branching support, Progress tracking, Context maintenance)

-   Use the date and time MCP server:

    -   Use `getCurrentDateTime_node` tool to get the current date and time in UTC format
    -   Add last updated date and time in UTC format to the `README.md` file

-   Use the websearch tool to find information on the internet when needed

### System & Environment Considerations

-   Target system: Windows 11 Home Single Language 23H2
-   Use semicolon (`;`) as the command separator in PowerShell commands, not `&&`
-   Use `New-Item -ItemType Directory -Path "path1", "path2", ... -Force` for creating directories in PowerShell
-   Use language-native path manipulation libraries (e.g., Node.js `path`) for robust path handling
-   Use package manager commands via the launch-process tool to add dependencies; do not edit package.json directly

### Error Handling & Debugging

-   First attempt to resolve errors autonomously using available tools
-   Perform systematic debugging: consult web resources, documentation, modify code, adjust configuration, retry
-   Report back only if an insurmountable blocker persists after exhausting all self-correction efforts

## Raw Idea Processing

When a user provides a raw idea, follow this process:

1. **Initial Processing**: Take the raw idea provided by the user and analyze it to identify ambiguities and generate relevant questions.

2. **Question Formulation**:

    - Ask all questions at once in a single response to avoid overwhelming the user with back-and-forth interactions
    - Format questions with clear options (e.g., option a, option b, etc.) whenever possible
    - For each question, provide a suggested answer that represents the most likely or optimal choice
    - Include an "Answer:" placeholder below each question so the user can easily respond without retyping the question

3. **Answer Handling**:

    - If the user leaves an answer blank, assume the suggested answer or the best possible option
    - Only ask for clarifications if absolutely necessary to proceed with generating the masterplan.md

4. **Masterplan Generation**:
    - After collecting all necessary information (either provided by the user or assumed), generate a comprehensive masterplan.md file
    - The masterplan.md should serve as a complete blueprint for the product, not just an MVP
    - Include all critical information needed for building the final product version without redundancy
    - Follow the structure outlined in the "Masterplan.md Structure" section above

## Additional Instructions

1. **Handling "Continue" Command**:

    - If the user responds with "continue" after you've asked questions, proceed with generating the masterplan.md using the suggested answers you provided
    - Treat this as an indication that the user accepts all your suggested answers and wants to move forward

2. **Feature Enhancement**:

    - Feel free to suggest additional features or requirements that would benefit the product
    - Ensure these suggestions are relevant to the core product idea and add genuine value
    - Include these suggestions in the appropriate sections of the masterplan.md

3. **Conciseness and Clarity**:

    - Create a concise masterplan.md without redundancy
    - Ensure all critical information for building the final product is included
    - Use clear, specific language that an AI code assistant can easily interpret and implement

4. **Raw Idea Placement**:
    - When the user provides a raw idea, it will be placed in the "The raw idea is:" section at the end of this file
    - Process this raw idea according to the instructions above



## The raw idea is:


make a react native expo app that will offer All PDF Tools.
no authentication.

it offers many PDF tools that are tailored to specific problems. All PDF tools are listed below.
# Project Specification: All PDF Tools - React Native Expo Application

## 1. Project Overview
Develop a comprehensive React Native Expo application that provides users with a complete suite of PDF manipulation tools. The application should function as an all-in-one PDF utility that allows users to perform various operations on PDF files directly from their mobile devices.

**Target Platforms:** iOS and Android and web (Expo)
**Authentication:** None required (serverless implementation)
**Core Value Proposition:** Provide a free, comprehensive set of PDF tools accessible from mobile devices without requiring user accounts or cloud storage.

## 2. Technical Requirements

### 2.1 Development Framework
- React Native with Expo SDK (latest stable version)
- JavaScript implementation
- No backend server requirements (fully client-side implementation)
ask for the following permissions as soon as the app is opened:
All Files Access

Allow access to manage all files

Allow this app to read, modify and delete all files on this device or any connected storage volumes. If granted, app may access files without your explicit knowledge.


show all pdf files in the device storage in a list view with preview thumbnail and file name


### 2.2 PDF Processing
- Implement PDF processing using React Native compatible libraries
- All PDF operations must be performed on-device to ensure privacy
- Support for PDF files up to 100MB in size
- Implement proper error handling for corrupted or incompatible PDF files
- Include progress indicators for operations that may take time

### 2.3 File Management
- Implement file picking from device storage
- Request appropriate permissions for file access
- Support saving modified PDFs to device storage
- Implement temporary file cleanup to prevent storage bloat

## 3. Feature Categories and Requirements

### 3.1 Document Organization
| Feature | Description | Priority |
|---------|-------------|----------|
| Merge PDF | Combine multiple PDFs into a single document with customizable order | High |
| Split PDF | Separate PDF into multiple documents by page ranges or individual pages | High |
| Remove Pages | Delete specific pages from a PDF document | High |
| Extract Pages | Save selected pages as a new PDF document | High |
| Organize Pages | Rearrange, rotate, or delete pages within a document | Medium |


### 3.2 Conversion Tools
| Feature | Description | Priority |
|---------|-------------|----------|
| JPG/PNG/webp/word/powerpoint/excel/html/text/rtf/epub/odt/odg/ods/odp/tiff/svg/heic/docx/pptx/xlsx/doc/ppt/xls/odt/odg/ods/odp/tiff/text/rtf/epub to PDF | Convert single or multiple files to PDF with customizable layout | High |
| PDF to JPG/PNG/webp/word/powerpoint/excel/html/text/rtf/epub/odt/odg/ods/odp/tiff/svg/heic/docx/pptx/xlsx/doc/ppt/xls/odt/odg/ods/odp/tiff/text/rtf/epub/html/secure PDF/PDF/A | Extract pages as images or convert entire document to  other files | High |

### 3.3 Editing Tools
| Feature | Description | Priority |
|---------|-------------|----------|
| Rotate PDF | Change page orientation (90°, 180°, 270°) for some or all pages | High |
| Add Page Numbers | Insert customizable page numbers with position and format options | Medium |
| Add Watermark | Apply text or image watermarks with opacity and position controls | Medium |
| Crop PDF | Adjust page margins or crop to specific dimensions | Medium |
| Basic Text/Image Editing | Add or edit simple text and images on PDF pages | Low |

### 3.4 Security Tools
| Feature | Description | Priority |
|---------|-------------|----------|
| Unlock PDF | Remove password protection from PDF files | High |
| Protect PDF | Add password protection to PDF files | High |
| Sign PDF | Add digital signatures to documents | Medium |
| Redact PDF | Permanently remove sensitive information from documents | Low |

### 3.5 Enhancement Tools
| Feature | Description | Priority |
|---------|-------------|----------|
| Compress PDF | Reduce file size while maintaining reasonable quality | High |
| Repair PDF | Attempt to fix corrupted PDF files | Medium |
| OCR PDF | Convert scanned documents to searchable text | Medium |
| Scan to PDF | Use device camera to scan documents and convert to PDF | Medium |
| Compare PDF | Show differences between two PDF documents | Low |

### 3.6 AI Features (Gemini API Integration)
| Feature | Description | Priority |
|---------|-------------|----------|
| Chat with PDF | Interactive Q&A with PDF content using RAG | High |
| AI PDF Summarizer | Generate concise summaries of PDF documents | High |
| Translate PDF | Translate PDF content between languages | Medium |
| AI Question Generator | Create questions based on PDF content | Low |

## 4. AI Implementation Requirements

### 4.1 Gemini API Integration
- Implement Google's Generative AI capabilities using the official SDK
- Required installation: `npm install @google/generative-ai`
- User must provide their own Gemini API key through a settings page
- Store API key securely using device secure storage (e.g., Expo SecureStore)
- Implement model selection functionality with appropriate defaults

### 4.2 Retrieval Augmented Generation (RAG)
- Extract and process text from PDFs for context
- Implement chunking strategy for large documents (e.g., page-based, section-based)
- Create appropriate prompts that combine user queries with document context
- Handle API rate limiting and token limitations gracefully
- Implement fallback mechanisms for when AI features are unavailable

### 4.3 Model Selection
- Provide interface for users to select from available Gemini models
- Implement model listing functionality as shown in the example code:
```javascript
async function listModels() {
  const models = await client.listModels();
  return models.map(model => ({
    name: model.name,
    description: model.description,
    inputTokenLimit: model.inputTokenLimit,
    outputTokenLimit: model.outputTokenLimit
  }));
}
```

ai features will be implemented via gemini api use the gemini key entered by the user in the settings page. provide a way for the user to enter the gemini key in the settings page. provide a way for user to select the model to be used for the ai features. the ai features will be implemented completely on the frontend. as there is no backend . use retrieval augmented generation for the ai features.
To get the list of available **Gemini models** (from Google’s Generative AI platform, formerly known as Bard/PaLM) using **JavaScript**, you typically use the **Google Generative AI SDK** (via REST API or Node.js client).

### 📦 1. Install Google Generative AI SDK (for Node.js)

```bash
npm install @google/generative-ai
```

---

### 🔑 2. Setup API Key

Get your API key from: [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)

```js
const genAI = require("@google/generative-ai");

const API_KEY = "YOUR_API_KEY_HERE";

const client = new genAI.GoogleGenerativeAI(API_KEY);
```

---

### 📋 3. List Available Models

Use the `listModels()` function:

```js
async function listModels() {
  const models = await client.listModels();
  models.forEach((model) => {
    console.log(`Model name: ${model.name}`);
    console.log(`  Description: ${model.description}`);
    console.log(`  Input token limit: ${model.inputTokenLimit}`);
    console.log(`  Output token limit: ${model.outputTokenLimit}`);
    console.log("---------------------------");
  });
}

listModels().catch(console.error);
```

---

### ✅ Output Example

This will return available models like:

```
Model name: models/gemini-1.5-pro
Model name: models/gemini-1.0-pro
Model name: models/gemini-1.5-flash
...
```

---

### 🧠 Notes

* You must have billing enabled and API access.
* Only accessible via `@google/generative-ai` client or direct REST calls to:

  ```
  GET https://generativelanguage.googleapis.com/v1beta/models
  ```


strictly include the above code in the masterplan.md file to get the list of available models. continue with suggested answers you provided
- Treat this as an indication that the user accepts all your suggested answers and wants to move forward