
make a nextjs app that will offer All PDF Tools.
use clerk for authentication. and mongodb for data storage. only 2 roles are needed: admin and user.
only 2 uses of each tool are allowed per day for free users. paid users can use the tools as much as they want.
users are upgraded to paid users when they pay for the subscription. the app will be hosted on render.com.
there will a admin panel to manage the users and their subscriptions.
the payment will be done using upi and admin will mannually verify the payment and upgrade the user to paid user.
the admin email id <NAME_EMAIL> and password is whyiswhen1234

it offers many PDF tools that are tailored to specific problems. All PDF tools are listed below.
Frequently used
Merge PDF
Split PDF
Compress PDF
Edit PDF
Sign PDF
PDF Converter
Convert to PDF
Convert PDF to ...
Images to PDF
PDF to images
Extract PDF images
Protect PDF
Unlock PDF
Rotate PDF pages
Remove PDF pages
Extract PDF pages
Rearrange PDF pages
Webpage to PDF
Create PDF job application
Create PDF with a camera
Add watermark
Add page numbers
View as PDF
PDF Overlay
Compare PDFs
Web optimize PDF
Annotate PDF
Redact PDF
Create PDF
More
PDF Reader
Create invoice
Remove PDF metadata
Edit PDF metadata
Flatten PDF
Crop PDF
Pages per sheet
Change PDF page size
Halve PDF pages
Fill out PDF
Repair PDF
Edit PDF bookmarks
Convert to PDF
Images to PDF
Word to PDF
PowerPoint to PDF
Excel to PDF
JPG to PDF
PNG to PDF
WEBP to PDF
HEIC to PDF
SVG to PDF
DOCX to PDF
PPTX to PDF
XLSX to PDF
DOC to PDF
PPT to PDF
XLS to PDF
ODT to PDF
ODG to PDF
ODS to PDF
ODP to PDF
TIFF to PDF
Text to PDF
RTF to PDF
EPUB to PDF
Convert from PDF
PDF to images
PDF to Word
PDF to PowerPoint
PDF to Excel
PDF to JPG
PDF to PNG
PDF to SVG
PDF to DOCX
PDF to PPTX
PDF to XLSX
PDF to ODT
PDF to ODS
PDF to ODP
PDF to Text
PDF to RTF
PDF to EPUB
PDF to HTML
PDF to secure PDF
PDF to PDF/A
Convert images
HEIC to JPG
HEIC to PNG
WEBP to JPG
WEBP to PNG
Tip
