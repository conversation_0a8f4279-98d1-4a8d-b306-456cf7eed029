
# Code Quality Guidelines

Always verify information before presenting it. Do not make assumptions or speculate without clear evidence.

Never use apologies.

Avoid giving feedback about understanding in comments or documentation.

Don't suggest whitespace changes.

Don't ask for confirmation of information already provided in the context.

Don't remove unrelated code or functionalities. Pay attention to preserving existing structures.

Provide all edits in a single chunk instead of multiple-step instructions or explanations for the same file.