enhance these user guidelines to be given to the Augment Agent, an agentic coding AI assistant developed by Augment Code, based on the Claude 3.7 Sonnet model by <PERSON><PERSON><PERSON>, dont leave any point, give different points for tools ,don't incorporate tools usage in common general points , give tools related point at the last, organize the points properly
keep everything in - only. give as many points as possible. dont leave anything, give in a structured heading and subheading format
# Augment Agent Operational Directives
## General Guidelines
- Make these user guidelines concise and to be given to the Augment Agent, an agentic coding AI assistant developed by Augment Code, based on the Claude 3.7 Sonnet model by <PERSON><PERSON><PERSON>, dont leave any point, give different points for tools, don't incorporate tools usage in common general points, give tools related point at the last, organize the points properly.
- Keep everything in - only. Don't leave anything. These guidelines should not remove any point, they should be concise and to the point.
## Tools Related Points
- Ensure to provide specific instructions for each tool available to the Augment Agent.
- Include examples of best practices for using each tool effectively.
- Highlight any limitations or considerations when using the tools.
- ... ...


# Augment Agent Operational Directives

## General Guidelines
- Important: Make these user guidelines concise and to be given to the Augment Agent, an agentic coding AI assistant developed by Augment Code, based on the Claude 3.7 Sonnet model by <PERSON><PERSON><PERSON>.
- Important: Keep everything in - only. Don't leave anything. These guidelines should not remove any point, they should be concise and to the point.

## Tools Related Points
- Ensure to provide specific instructions for each tool available to the Augment Agent.
- Include examples of best practices for using each tool effectively.
- Highlight any limitations or considerations when using the tools.
- You have access to several tools that help you assist me effectively: [list of tools].
- Important: Combine references to the same tool in one point to avoid redundancy.
- Important: Combine references to the same MCP server, also called tools, in one point to avoid redundancy.

## MCP Servers Related Points
- Important: Provide clear instructions on how to interact with MCP servers.
- Important: Outline any specific protocols or considerations when using MCP servers.

- the above points are the user guidelines to be given to the Augment Agent, an agentic coding AI assistant developed by Augment Code, based on the Claude 3.7 Sonnet model by Anthropic,
 restructure the points to be arranged and sorted in the manner that the similar points are grouped together in a logical order in headings, subheadings, and sub-subheadings and so on.
 don't leave any important point, give as many points as necessary.
  give the point with - only. give the tools related points and mcp servers related points at the last.
   mcp stands for model context protocol. write important points with Important: in front of it.
    for specifying the points use - only.  , organize the points properly. give the points in a structured heading ,
    subheading and sub-subheading and so on  format.
    format the points in a way that it is easy to read and understand.
     format the headings and subheadings and so on in markdown format.
      if you are referring to the same tool in different points, combine them in  one point.
       the points can be as short as necessary. don't reference the same mcp server also called tools in different points, combine them in one point. bring the last points which are under you have access to several tools that help you assist me effectively: to the inside of the tools related points. don't give them as it is, restructure them in the manner that they fit in the tools related points. add the following point properly, always mention which tool or mcp server will help in which task , for example, if you want to search the web, you can use web-search tool to search the web. if you want to run a command, you can use launch-process tool to run the command. remove unnecessary guide lines. the guidelines can be used to give to the Augment Agent, an agentic coding AI assistant developed by Augment Code, based on the Claude 3.7 Sonnet model by Anthropic to create the software, refactor the code, debug the code, and so on. the guidelines should be used to give to the Augment Agent, an agentic coding AI assistant developed by Augment Code, based on the Claude 3.7 Sonnet model by Anthropic to create the software, refactor the code, debug the code, and so on. strictly don't ask for the user input until the task list is completed. don't ask to the user to approve the plan or similar things. don't bold anything use markdown heading format if you want to emphasize anything