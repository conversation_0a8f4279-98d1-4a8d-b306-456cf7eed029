
give me a prd for a react native expo app that

 user can share the youtube videos links and it provides the summary of the video, the summary is generated by Gemini 2.0 Flash-Lite ,the summary is stored in superbase, add authentication and authorization using superbase


- The app allows the user to read aloud the summary using text-to-speech functionality.
- the read aloud feature should be available for all summary types.
- The app should provide a way for users to adjust the speed of the text-to-speech feature up to 16x speed.
- The app should provide a way for users to adjust the pitch of the text-to-speech feature.
- The app should provide a way for users to adjust the voice of the text-to-speech feature.
- Provide a way for users to select between summary types: Brief, Detailed, Key Point.
- Default type should be set to Brief.
- The app should provide a way for users to select between summary lengths: Short, Medium, Long.
- Default length should be set to Medium.

- The app should allow users to paste YouTube video links directly into a text input field for summary generation.
Besides pasting,  users should be able to share a YouTube link directly from the YouTube app into your app
it is building a react native expo app with the following setup:
strictly should the initial client-side URL validation be? Just check for youtube.com or youtu.be, or attempt a more complex regex? (Backend validation will be the final check).
superbase is used for authentication and storage of summaries with video links.(show the video link in the summary in hamburg menu)
 there Should be a way for the user to cancel a summary generation request that is in progress
When displaying a summary,  the video's title and thumbnail should always be fetched and displayed alongside it?
In the history list, information should be shown for each entry Thumbnail + Title
 users Shouldbe able to delete summaries from their history?

the fallback plan if a video doesn't have transcripts or captions available is  the Should app inform the user it cannot summarize this video?
fetching the video_title, video_thumbnail is not critical?  summary generation Should not fail if the title, thumbnail can't be retrieved?
- this app will be android, ios and web compatible. and it will be a progressive web app which can be installed on the home screen of the mobile device.
 add social login options (Google, etc.) via Supabase Auth
Sharing generated summaries directly from the app.
- give the summary in a card format with the following details:
  - Video Title
  - Video Thumbnail
  - Summary Text(parsed in markdown format)
  - Summary Type (Brief, Detailed, Key Point)
  - Summary Length (Short, Medium, Long)
  - Read Aloud Button
  - Share Button
  - Delete Button
  - Edit Button (to change summary type and length)
- The app should have a history screen that displays all previously generated summaries in a list format.
- the summary should be in the markdown format.
- the app should have a settings screen where users can adjust the text-to-speech settings (speed, pitch, voice).
-


- Frontend: React Native Expo app using JavaScript.
- ML: Uses Gemini 2.0 Flash-Lite.
- Backend: Express.js + Gemini 2.0 Flash-Lite API for ai + ytdlp-nodejs for youtube video link validation and fetching video title and thumbnail and transcript it can be install with `npm i ytdlp-nodejs`
- Project Structure:
  - frontend/ folder for React Native app code.
  - backend/ folder for backend code.
  - Storage: superbase for storage
- Authentication: Use superbase for authentication and authorization.
- Additional Features:
  - Implement error handling for video link validation.
  - Ensure the app is optimized for performance across all platforms.
don't leave anything for future , this is a prd for final product not a mvp
- Make Project Structure as modular as possible.
